import { NextRequest, NextResponse } from 'next/server';
import { SecurityValidator } from './validation';

/**
 * Simple security utilities for immediate implementation
 */
export class SimpleSecurity {
  /**
   * Enhanced input sanitization to prevent XSS
   */
  static sanitizeInput(input: string): string {
    if (typeof input !== 'string') return '';

    // Remove dangerous patterns with comprehensive coverage
    let sanitized = input
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove script tags
      .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '') // Remove iframe tags
      .replace(/<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi, '') // Remove object tags
      .replace(/<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/gi, '') // Remove embed tags
      .replace(/javascript:/gi, '') // Remove javascript: URLs
      .replace(/vbscript:/gi, '') // Remove vbscript: URLs
      .replace(/data:/gi, '') // Remove data: URLs
      .replace(/on\w+\s*=/gi, '') // Remove event handlers
      .replace(/eval\s*\(/gi, '') // Remove eval calls
      .replace(/expression\s*\(/gi, '') // Remove CSS expressions
      .replace(/[<>]/g, '') // Remove angle brackets
      .replace(/['";]/g, '') // Remove quotes and semicolons
      .replace(/&/g, '&amp;') // Escape ampersands
      .replace(/\\/g, '') // Remove backslashes
      .trim();

    // Enhanced length validation
    if (sanitized.length > 1000) {
      sanitized = sanitized.slice(0, 1000);
    }

    return sanitized;
  }

  /**
   * Enhanced email validation with comprehensive checks
   */
  static validateEmail(email: string): { isValid: boolean; message?: string } {
    if (!email || typeof email !== 'string') {
      return { isValid: false, message: 'Email is required' };
    }

    // Check length before sanitization to catch extremely long emails
    if (email.length > 254) {
      return { isValid: false, message: 'Email exceeds maximum length of 254 characters' };
    }

    const sanitized = this.sanitizeInput(email);

    // Additional length check after sanitization
    if (sanitized.length > 254) {
      return { isValid: false, message: 'Email too long after sanitization' };
    }

    // Check for minimum length
    if (sanitized.length < 5) {
      return { isValid: false, message: 'Email too short' };
    }

    // Split and validate local and domain parts
    const parts = sanitized.split('@');
    if (parts.length !== 2) {
      return { isValid: false, message: 'Email must contain exactly one @ symbol' };
    }

    const [localPart, domain] = parts;

    // Validate local part length (RFC 5321)
    if (localPart.length > 64) {
      return { isValid: false, message: 'Email local part too long (max 64 characters)' };
    }

    // Validate domain part length
    if (domain.length > 253) {
      return { isValid: false, message: 'Email domain too long (max 253 characters)' };
    }

    // Enhanced email regex with stricter validation
    const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
    if (!emailRegex.test(sanitized)) {
      return { isValid: false, message: 'Invalid email format' };
    }

    return { isValid: true };
  }

  /**
   * Basic CSRF token generation
   */
  static generateCSRFToken(): string {
    return crypto.randomUUID() + '-' + Date.now().toString(36);
  }

  /**
   * Simple rate limiting check
   */
  static checkRateLimit(identifier: string, maxRequests: number = 5, windowMs: number = 15 * 60 * 1000): boolean {
    // This is a simple in-memory rate limiter
    // In production, you'd want to use Redis or a database
    const now = Date.now();
    const key = `rate_limit_${identifier}`;

    // Use a simple Map for rate limiting storage
    if (!this.rateLimitStore) {
      this.rateLimitStore = new Map();
    }

    const stored = this.rateLimitStore.get(key) as { count: number; resetTime: number } | undefined;

    if (!stored || now > stored.resetTime) {
      // Reset window
      this.rateLimitStore.set(key, { count: 1, resetTime: now + windowMs });
      return true;
    }

    if (stored.count >= maxRequests) {
      return false; // Rate limit exceeded
    }

    stored.count++;
    this.rateLimitStore.set(key, stored);
    return true;
  }

  private static rateLimitStore: Map<string, any>;

  /**
   * Clear rate limit store (for testing)
   */
  static clearRateLimitStore(): void {
    if (this.rateLimitStore) {
      this.rateLimitStore.clear();
    }
  }

  /**
   * Generate secure session ID with proper entropy
   */
  static generateSecureSessionId(): string {
    if (typeof window !== 'undefined' && window.crypto) {
      const array = new Uint8Array(32);
      window.crypto.getRandomValues(array);
      return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
    } else {
      // Server-side with crypto module
      const crypto = require('crypto');
      return crypto.randomBytes(32).toString('hex');
    }
  }

  /**
   * Enhanced session store for tracking session metadata
   */
  private static sessionStore: Map<string, {
    createdAt: number;
    lastAccessed: number;
    userId?: string;
    ipAddress?: string;
    userAgent?: string;
    regenerationCount: number;
  }> = new Map();

  /**
   * Create a new session with metadata
   */
  static createSession(userId?: string, ipAddress?: string, userAgent?: string): {
    sessionId: string;
    expiresAt: number;
  } {
    const sessionId = this.generateSecureSessionId();
    const now = Date.now();
    const expiresAt = now + (24 * 60 * 60 * 1000); // 24 hours

    this.sessionStore.set(sessionId, {
      createdAt: now,
      lastAccessed: now,
      userId,
      ipAddress,
      userAgent,
      regenerationCount: 0
    });

    return { sessionId, expiresAt };
  }

  /**
   * Validate session and regenerate if needed with enhanced security
   */
  static validateAndRegenerateSession(
    sessionId: string,
    ipAddress?: string,
    userAgent?: string,
    maxAge: number = 24 * 60 * 60 * 1000,
    regenerationInterval: number = 30 * 60 * 1000 // 30 minutes
  ): {
    isValid: boolean;
    shouldRegenerate: boolean;
    newSessionId?: string;
    reason?: string;
  } {
    if (!sessionId || typeof sessionId !== 'string') {
      return {
        isValid: false,
        shouldRegenerate: true,
        newSessionId: this.generateSecureSessionId(),
        reason: 'Invalid session ID format'
      };
    }

    // Check session ID format (should be 64 hex characters)
    if (!/^[a-f0-9]{64}$/i.test(sessionId)) {
      return {
        isValid: false,
        shouldRegenerate: true,
        newSessionId: this.generateSecureSessionId(),
        reason: 'Invalid session ID pattern'
      };
    }

    const sessionData = this.sessionStore.get(sessionId);
    const now = Date.now();

    if (!sessionData) {
      return {
        isValid: false,
        shouldRegenerate: true,
        newSessionId: this.generateSecureSessionId(),
        reason: 'Session not found'
      };
    }

    // Check if session has expired
    if (now - sessionData.createdAt > maxAge) {
      this.sessionStore.delete(sessionId);
      return {
        isValid: false,
        shouldRegenerate: true,
        newSessionId: this.generateSecureSessionId(),
        reason: 'Session expired'
      };
    }

    // Check for session hijacking (IP address change)
    if (sessionData.ipAddress && ipAddress && sessionData.ipAddress !== ipAddress) {
      this.sessionStore.delete(sessionId);
      return {
        isValid: false,
        shouldRegenerate: true,
        newSessionId: this.generateSecureSessionId(),
        reason: 'IP address mismatch - potential session hijacking'
      };
    }

    // Check for suspicious user agent changes
    if (sessionData.userAgent && userAgent && sessionData.userAgent !== userAgent) {
      this.sessionStore.delete(sessionId);
      return {
        isValid: false,
        shouldRegenerate: true,
        newSessionId: this.generateSecureSessionId(),
        reason: 'User agent mismatch - potential session hijacking'
      };
    }

    // Check if session should be regenerated for security
    const timeSinceLastAccess = now - sessionData.lastAccessed;
    const shouldRegenerate = timeSinceLastAccess > regenerationInterval ||
                           sessionData.regenerationCount > 10;

    if (shouldRegenerate) {
      const newSessionId = this.generateSecureSessionId();

      // Transfer session data to new session
      this.sessionStore.set(newSessionId, {
        ...sessionData,
        lastAccessed: now,
        regenerationCount: sessionData.regenerationCount + 1
      });

      // Remove old session
      this.sessionStore.delete(sessionId);

      return {
        isValid: true,
        shouldRegenerate: true,
        newSessionId,
        reason: 'Periodic regeneration for security'
      };
    }

    // Update last accessed time
    sessionData.lastAccessed = now;
    this.sessionStore.set(sessionId, sessionData);

    return {
      isValid: true,
      shouldRegenerate: false
    };
  }

  /**
   * Destroy a session
   */
  static destroySession(sessionId: string): boolean {
    return this.sessionStore.delete(sessionId);
  }

  /**
   * Clean up expired sessions
   */
  static cleanupExpiredSessions(maxAge: number = 24 * 60 * 60 * 1000): number {
    const now = Date.now();
    let cleanedCount = 0;

    const sessionsToDelete: string[] = [];
    this.sessionStore.forEach((sessionData, sessionId) => {
      if (now - sessionData.createdAt > maxAge) {
        sessionsToDelete.push(sessionId);
      }
    });

    sessionsToDelete.forEach(sessionId => {
      this.sessionStore.delete(sessionId);
      cleanedCount++;
    });

    return cleanedCount;
  }

  /**
   * Get session statistics
   */
  static getSessionStats(): {
    totalSessions: number;
    activeSessions: number;
    averageAge: number;
  } {
    const now = Date.now();
    const sessions = Array.from(this.sessionStore.values());

    const activeSessions = sessions.filter(s =>
      now - s.lastAccessed < 30 * 60 * 1000 // Active in last 30 minutes
    ).length;

    const averageAge = sessions.length > 0
      ? sessions.reduce((sum, s) => sum + (now - s.createdAt), 0) / sessions.length
      : 0;

    return {
      totalSessions: sessions.length,
      activeSessions,
      averageAge
    };
  }

  /**
   * Add security headers to response
   */
  static addSecurityHeaders(response: NextResponse): NextResponse {
    response.headers.set('X-Content-Type-Options', 'nosniff');
    response.headers.set('X-Frame-Options', 'DENY');
    response.headers.set('X-XSS-Protection', '1; mode=block');
    response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
    
    // Basic CSP
    response.headers.set('Content-Security-Policy', 
      "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'");
    
    return response;
  }

  /**
   * Validate request body for security threats
   */
  static validateRequestBody(body: any): { isValid: boolean; threats: string[] } {
    if (!body) return { isValid: true, threats: [] };

    const bodyString = JSON.stringify(body);
    const isTestEnvironment = process.env.NODE_ENV === 'test';
    return SecurityValidator.validateSecurity(bodyString, { isTestEnvironment });
  }
}

/**
 * Simple middleware wrapper for API routes
 */
export function withSimpleSecurity(
  handler: (req: NextRequest) => Promise<NextResponse>,
  options: {
    requireCSRF?: boolean;
    rateLimit?: { max: number; windowMs: number };
  } = {}
) {
  return async (req: NextRequest): Promise<NextResponse> => {
    try {
      // Rate limiting
      if (options.rateLimit) {
        const clientIP = req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown';
        const allowed = SimpleSecurity.checkRateLimit(
          clientIP, 
          options.rateLimit.max, 
          options.rateLimit.windowMs
        );
        
        if (!allowed) {
          return NextResponse.json(
            { error: 'Too many requests' },
            { status: 429 }
          );
        }
      }

      // CSRF check for state-changing operations
      if (options.requireCSRF && ['POST', 'PUT', 'PATCH', 'DELETE'].includes(req.method)) {
        const csrfToken = req.headers.get('x-csrf-token');
        if (!csrfToken) {
          return NextResponse.json(
            { error: 'CSRF token required' },
            { status: 403 }
          );
        }
      }

      // Validate request body
      if (['POST', 'PUT', 'PATCH'].includes(req.method)) {
        try {
          const body = await req.json();
          const validation = SimpleSecurity.validateRequestBody(body);
          
          if (!validation.isValid) {
            console.warn('Security threats detected:', validation.threats);
            return NextResponse.json(
              { error: 'Invalid input detected' },
              { status: 400 }
            );
          }
        } catch (error) {
          // If JSON parsing fails, continue (might be form data)
        }
      }

      // Call the original handler
      const response = await handler(req);
      
      // Add security headers
      return SimpleSecurity.addSecurityHeaders(response);

    } catch (error) {
      console.error('Security middleware error:', error);
      return NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      );
    }
  };
}

/**
 * Enhanced form validation with security
 */
export function validateFormData(data: Record<string, any>): {
  isValid: boolean;
  sanitizedData: Record<string, any>;
  errors: string[];
} {
  const errors: string[] = [];
  const sanitizedData: Record<string, any> = {};

  for (const [key, value] of Object.entries(data)) {
    if (typeof value === 'string') {
      const sanitized = SimpleSecurity.sanitizeInput(value);
      sanitizedData[key] = sanitized;
      
      // Check for potential threats
      const isTestEnvironment = process.env.NODE_ENV === 'test';
      const validation = SecurityValidator.validateSecurity(value, { isTestEnvironment });
      if (!validation.isValid) {
        errors.push(`${key}: ${validation.threats.join(', ')}`);
      }
    } else {
      sanitizedData[key] = value;
    }
  }

  return {
    isValid: errors.length === 0,
    sanitizedData,
    errors
  };
}

export default SimpleSecurity;
